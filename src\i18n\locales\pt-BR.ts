export default {
  avaliacao: {
    title: 'Avaliação de Desempenho',
    description: 'Gerencie avaliações de desempenho dos colaboradores',
    dashboard: 'Dashboard',
    moduleStatus: 'Status do módulo',
    statusLoading: 'Carregando...',
    statusOnline: 'Online',
    statusError: 'Erro',
    novaAvaliacao: 'Nova Avaliação',
    novaAvaliacaoDesc: 'Crie uma nova avaliação de desempenho',
    nova: {
      loadFuncionariosError: 'Ocorreu um erro ao carregar os funcionários. Por favor, tente novamente.',
      requiredFieldsError: 'Por favor, preencha todos os campos obrigatórios.',
      createError: 'Ocorreu um erro ao criar a avaliação. Por favor, tente novamente.',
      createSuccess: 'Avaliação criada com sucesso! Redirecionando...',
      selectAvaliador: 'Selecione um avaliador',
      selectFuncionario: 'Selecione um funcionário'
    },
    observacoes: 'Observações',
    editAvaliacao: 'Editar Avaliação',
    redirectingToList: 'Redirecionando para a lista de avaliações em alguns segundos...',
    avaliacoes: {
      title: 'Avaliações',
      description: 'Gerencie avaliações de desempenho',
      empty: 'Nenhuma avaliação cadastrada',
      emptyDesc: 'Comece criando uma nova avaliação de desempenho.'
    },
    searchPlaceholder: 'Pesquisar avaliações...',
    trashLink: 'Lixeira',
    movedToTrashSuccess: 'Avaliação movida para a lixeira com sucesso!',
    moveToTrashError: 'Ocorreu um erro ao mover a avaliação para a lixeira. Por favor, tente novamente.',
    confirmMoveToTrash: 'Tem certeza que deseja mover esta avaliação para a lixeira? Ela será excluída permanentemente após 30 dias.',
    trash: {
      title: 'Lixeira de Avaliações',
      backToList: 'Voltar para a lista',
      searchPlaceholder: 'Pesquisar avaliações na lixeira...',
      empty: 'Nenhuma avaliação na lixeira.',
      loadError: 'Ocorreu um erro ao carregar as avaliações da lixeira. Por favor, tente novamente.',
      confirmRestore: 'Tem certeza que deseja restaurar esta avaliação?',
      restoreSuccess: 'Avaliação restaurada com sucesso!',
      restoreError: 'Ocorreu um erro ao restaurar a avaliação. Por favor, tente novamente.',
      confirmDelete: 'Tem certeza que deseja excluir permanentemente esta avaliação? Esta ação não pode ser desfeita.',
      deleteSuccess: 'Avaliação excluída permanentemente!',
      deleteError: 'Ocorreu um erro ao excluir a avaliação. Por favor, tente novamente.',
      deletedAt: 'Excluído em',
      restore: 'Restaurar',
      deletePermanently: 'Excluir permanentemente'
    },
    createdAt: 'Data de Criação',
    actions: 'Ações',
    status: {
      title: 'Status',
      pending: 'Pendente',
      inProgress: 'Em Progresso',
      completed: 'Concluída',
      archived: 'Arquivada'
    },
    debug: {
      title: 'Depuração de Avaliações',
      description: 'Ferramenta para identificar problemas na rota de avaliações',
      systemDiagnostic: 'Diagnóstico do Sistema',
      authStatus: 'Status de Autenticação',
      tokenStatus: 'Status do Token',
      routerStatus: 'Status do Router',
      apiStatus: 'Status da API',
      apiData: 'Dados da API',
      navigationLinks: 'Links para Navegação',
      directLinks: 'Links Diretos (window.location)',
      routerLinks: 'Links via Router',
      linkComponentLinks: 'Links via Link Component',
      dashboardLink: 'Dashboard de Avaliações',
      evaluationListLink: 'Lista de Avaliações'
    },
    funcionarios: {
      title: 'Funcionários',
      noFuncionarios: 'Nenhum funcionário cadastrado',
      addFuncionario: 'Adicionar Funcionário',
      detalhes: 'Detalhes do Funcionário',
      detalhesDesc: 'Visualize informações e avaliações do funcionário',
      funcionarioNaoEncontrado: 'Funcionário não encontrado',
      nome: 'Nome',
      cargo: 'Cargo',
      departamento: 'Departamento',
      dataAdmissao: 'Data de Admissão',
      email: 'Email',
      telefone: 'Telefone',
      status: 'Status'
    },
    relatorios: {
      title: 'Relatórios',
      description: 'Gere relatórios de avaliação de desempenho',
      tipoRelatorio: 'Tipo de Relatório',
      periodoRelatorio: 'Período',
      tipoOptions: {
        individual: 'Individual',
        departamento: 'Por Departamento',
        geral: 'Geral'
      },
      exportarExcel: 'Exportar Excel',
      exportarPDF: 'Exportar PDF',
      gerarRelatorio: 'Gerar Relatório'
    },
    importacao: {
      title: 'Importação',
      description: 'Importe dados de avaliação de desempenho',
      instructionsText: 'Arraste e solte um arquivo CSV ou clique para selecionar',
      fileFormat: 'Formatos suportados: CSV, XLS, XLSX',
      selectFile: 'Selecionar Arquivo',
      template: 'Baixar Modelo',
      upload: 'Importar Dados',
      processing: 'Processando...',
      success: 'Importação concluída com sucesso',
      rowsProcessed: 'Linhas processadas',
      rowsImported: 'Linhas importadas',
      rowsSkipped: 'Linhas ignoradas',
      rowsWithErrors: 'Linhas com erros',
      preview: 'Pré-visualização',
      instructions: 'Instruções',
      importAvaliacoes: 'Importar Avaliações',
      importFuncionarios: 'Importar Funcionários',
      importCriterios: 'Importar Critérios',
      invalidFileType: 'Tipo de arquivo inválido. Por favor, selecione um arquivo CSV, XLSX ou XLS.'
    },
    periodoOptions: {
      mensal: 'Mensal',
      trimestral: 'Trimestral',
      semestral: 'Semestral',
      anual: 'Anual'
    },
    funcionario: 'Funcionário',
    avaliador: 'Avaliador',
    periodo: 'Período',
    dataInicio: 'Data de Início',
    dataFim: 'Data de Fim',

    criterios: 'Critérios de Avaliação',
    semCriterios: 'Nenhum critério de avaliação disponível',
    historicoAvaliacoes: 'Histórico de Avaliações',
    semAvaliacoes: 'Este funcionário ainda não possui avaliações registradas',
    noAvaliacoes: 'Nenhuma avaliação registrada',
    avaliacaoGerenteLogado: 'Você está criando esta avaliação como gerente avaliador'
  },

  auth: {
    phoneLogin: 'Login com Telefone',
    emailLogin: 'Login com Email',
    email: 'Email',
    usePhone: 'Usar telefone',
    useEmail: 'Usar email',
    enterPhone: 'Digite seu número de telefone',
    enterEmail: 'Digite seu email',
    enterCode: 'Digite o código de verificação',
    sendCode: 'Enviar código',
    verifyCode: 'Verificar código',
    codeSent: 'Código enviado para seu telefone',
    codeResent: 'Código reenviado para seu telefone',
    codeSentEmail: 'Código enviado para seu email',
    codeResentEmail: 'Código reenviado para seu email',
    resendCode: 'Reenviar código',
    codeSentAgain: 'Código reenviado com sucesso',
    resendCodeError: 'Erro ao reenviar código. Tente novamente',
    invalidCode: 'Código inválido',
    invalidPhoneNumber: 'Por favor, insira um número de telefone válido',
    invalidEmail: 'Por favor, insira um email válido',
    codeError: 'Erro ao enviar código de verificação. Tente novamente',
    requestError: 'Erro ao processar a solicitação. Tente novamente',
    loginSuccess: 'Login realizado com sucesso',
    loginError: 'Erro ao realizar login',
    sessionExpired: 'Sua sessão expirou, faça login novamente',
    notAuthorized: 'Você não tem permissão para acessar esta página',
    welcomeBack: 'Bem-vindo de volta',
    accessAccount: 'Acesse Sua Conta',
    verifyPhone: 'Verifique seu telefone',
    enterPassword: 'Digite sua senha',
    pendingRequest: 'Solicitação Pendente',
    unauthorizedAccess: 'Acesso Não Autorizado',
    pendingRequestTitle: 'Solicitação Pendente',
    pendingRequestMessage: 'Sua solicitação de acesso está pendente de aprovação por um administrador.',
    pendingRequestNotification: 'Você receberá uma notificação quando sua solicitação for aprovada.',
    unauthorizedAccessTitle: 'Acesso Não Autorizado',
    unauthorizedAccessMessage: 'Você não está autorizado a acessar o sistema. Uma solicitação de acesso foi criada e está aguardando aprovação.',
    unauthorizedAccessContact: 'Entre em contato com o administrador do sistema para mais informações.',
    inviteCode: 'Código de Convite',
    hideInviteCode: 'Ocultar código de convite',
    haveInviteCode: 'Tenho um código de convite',
    enterInviteCode: 'Digite o código de convite',
    inviteCodeHelp: 'Se você recebeu um código de convite, digite-o aqui para acessar o sistema',
    password: 'Senha',
    passwordHelp: 'Digite sua senha para acessar sua conta',
    login: 'Entrar',
    rememberMe: 'Lembrar-me',
    registeredUserMessage: 'Você já possui uma conta cadastrada. Por favor, digite sua senha para acessar o sistema.',
    invalidPassword: 'Senha incorreta. Por favor, tente novamente',
    phoneNumber: 'Número de Telefone',
    phoneNumberHelp: 'Digite seu número de telefone com código do país, começando com + (ex: +5511999999999). O número deve estar no formato internacional.',
    verificationCode: 'Código de Verificação',
    verificationCodeHelp: 'Digite o código de verificação enviado para seu telefone',
    backToStart: 'Voltar para o início',
    continue: 'Continuar',
    sending: 'Enviando...',
    verifying: 'Verificando...',
    loggingIn: 'Entrando...',
    forgotPassword: 'Esqueci minha senha',
    resetPassword: 'Redefinir senha',
    sendResetLink: 'Enviar link de redefinição',
    resetLinkSent: 'Link de redefinição enviado',
    resetLinkSentEmailDescription: 'Enviamos um link para redefinir sua senha para o email informado. Verifique sua caixa de entrada e spam.',
    resetLinkSentPhoneDescription: 'Enviamos um link para redefinir sua senha para o número de telefone informado.',
    resetPasswordEmailDescription: 'Você receberá um email com instruções para redefinir sua senha.',
    resetPasswordPhoneDescription: 'Você receberá um SMS com instruções para redefinir sua senha.',
    passwordRequirements: 'A senha deve ter pelo menos 8 caracteres.',
    completeRegistration: 'Complete seu cadastro para acessar o sistema.',
    passwordMismatch: 'As senhas não coincidem.',
    passwordTooShort: 'A senha deve ter pelo menos 8 caracteres.',
    passwordResetSuccess: 'Senha redefinida com sucesso',
    passwordResetSuccessDescription: 'Sua senha foi redefinida com sucesso. Você será redirecionado para a página de login em instantes.',
    resetPasswordError: 'Erro ao redefinir senha',
    invalidOrExpiredToken: 'Link inválido ou expirado',
    tokenVerificationError: 'Erro ao verificar o link de redefinição',
    accountLocked: 'Conta bloqueada',
    accountLockedDescription: 'Sua conta foi temporariamente bloqueada devido a múltiplas tentativas de login. Tente novamente mais tarde.',
    newPassword: 'Nova senha',
    confirmPassword: 'Confirmar senha',
    backToLogin: 'Voltar para o login',
    emailPlaceholder: '<EMAIL>',
    backToIdentifier: 'Voltar para identificação',
    setPasswordWithInvite: 'Definir senha com código de convite',
    notRegistered: 'Ainda não tem cadastro?',
    createAccount: 'Criar conta'
  },
  register: {
    title: 'Complete seu cadastro',
    subtitle: 'Preencha os dados abaixo para criar sua conta',
    firstName: 'Nome',
    lastName: 'Sobrenome',
    email: 'Email',
    phoneNumber: 'Telefone',
    phone: 'Telefone',
    cpf: 'CPF',
    position: 'Cargo',
    department: 'Departamento',
    password: 'Senha',
    confirmPassword: 'Confirmar senha',
    submit: 'Registrar',
    loading: 'Registrando...',
    success: 'Registro realizado com sucesso',
    alreadyHaveAccount: 'Já tem uma conta?',
    login: 'Faça login',
    error: {
      requiredFields: 'Nome e sobrenome são obrigatórios',
      phoneRequired: 'Telefone é obrigatório',
      cpfRequired: 'CPF é obrigatório',
      cargoRequired: 'Cargo é obrigatório',
      invalidCpf: 'CPF deve ter 11 dígitos',
      generic: 'Erro ao realizar registro'
    },
    requiredFields: 'Campos marcados com * são obrigatórios',
    firstNamePlaceholder: 'Seu nome',
    lastNamePlaceholder: 'Seu sobrenome',
    emailPlaceholder: '<EMAIL>',
    phoneNumberPlaceholder: '+55 (11) 99999-9999',
    phonePlaceholder: '+55 (11) 99999-9999',
    cpfPlaceholder: '000.000.000-00',
    positionPlaceholder: 'Ex: Desenvolvedor',
    departmentPlaceholder: 'Ex: Tecnologia',
    passwordPlaceholder: 'Mínimo 8 caracteres',
    confirmPasswordPlaceholder: 'Repita a senha',
    completeRegistration: 'Complete seu cadastro para acessar o sistema',
    registrationIncomplete: 'Registro incompleto',
    registrationSuccessRedirect: 'Registro concluído! Redirecionando para o dashboard...',
    protocolNumber: 'Número do Protocolo',
    backToLogin: 'Voltar ao Login',
    successMessage: 'Seu registro foi concluído com sucesso. Verifique seu e-mail para confirmar seu cadastro.',
    successMessageActive: 'Seu registro foi concluído com sucesso e sua conta já está ativa.',
    loginNow: 'Você já pode fazer login imediatamente com seu e-mail ou telefone.',
    checkEmail: 'Verifique seu e-mail para ativar sua conta.',
    notRegisteredYet: 'Este email/telefone ainda não está cadastrado. Por favor, complete seu cadastro abaixo.'
  },
  dashboard: {
    title: 'Painel',
    welcome: 'Bem-vindo ao Painel ABZ',
    quickAccess: 'Acesso Rápido',
    recentDocuments: 'Documentos Recentes',
    announcements: 'Anúncios',
    statistics: 'Estatísticas',
    tasks: 'Tarefas',
    calendar: 'Calendário',
    notifications: 'Notificações',
    noNotifications: 'Nenhuma notificação',
    viewAll: 'Ver todos',
    logisticsPanel: 'Painel de Logística ABZ Group',
    welcomeMessage: 'Bem-vindo ao centro de recursos para colaboradores da logística.',
    quickAccessFeatures: 'Acesso Rápido às Funcionalidades',
    centralizedPanel: 'Este painel centraliza todos os recursos necessários para o setor de logística.',
    contactSupport: 'Para mais informações ou suporte, entre em contato com o departamento de TI.',
    accessAdminPanel: 'Acessar Painel de Administração',
    access: 'Acessar',
    noCards: 'Nenhum card disponível',
  },
  menu: {
    dashboard: 'Painel',
    manualLogistico: 'Manual Logístico',
    procedimentoLogistica: 'Procedimento Logística',
    politicas: 'Políticas',
    procedimentosGerais: 'Procedimentos Gerais',
    calendario: 'Calendário',
    abzNews: 'ABZ News',
    reembolso: 'Reembolso',
    contracheque: 'Contracheque',
    ponto: 'Ponto',
    folhaPagamento: 'Folha de Pagamento',
    avaliacao: 'Avaliação de Desempenho',
    administracao: 'Administração',
    usuariosAutorizados: 'Usuários Autorizados',
  },
  cards: {
    // Traduções padrão para os cards do menu
    manualColaborador: 'Manual do Colaborador',
    manualColaboradorDesc: 'Acesse o manual completo do colaborador.',
    procedimentosLogistica: 'Procedimentos de Logística',
    procedimentosLogisticaDesc: 'Consulte os procedimentos padrões da área.',
    politicas: 'Políticas',
    politicasDesc: 'Consulte as políticas da empresa.',
    procedimentosGerais: 'Procedimentos Gerais',
    procedimentosGeraisDesc: 'Consulte os procedimentos gerais da empresa.',
    calendario: 'Calendário',
    calendarioDesc: 'Visualize eventos e datas importantes.',
    noticias: 'Notícias',
    noticiasDesc: 'Fique por dentro das novidades da empresa.',
    reembolso: 'Reembolso',
    reembolsoDesc: 'Solicite reembolso de despesas.',
    contracheque: 'Contracheque',
    contrachequeDesc: 'Acesse seus contracheques.',
    ponto: 'Ponto',
    pontoDesc: 'Registre seu ponto e consulte seu histórico.',
    avaliacao: 'Avaliação de Desempenho',
    avaliacaoDesc: 'Gerencie avaliações de desempenho dos colaboradores.',
    folhaPagamento: 'Folha de Pagamento',
    folhaPagamentoDesc: 'Gestão completa de folha de pagamento e cálculos trabalhistas.',
    admin: 'Admin',
    adminDesc: 'Painel administrativo',

    // Traduções para os cards do banco de dados (por ID)
    // Versão sem hífens (compatibilidade)
    '6377431f4afa448bb46a8321a5870f37': 'Manual',
    '6377431f4afa448bb46a8321a5870f37Desc': 'Acesse o manual da empresa',
    'c40a97fd70a543f1af4d960efabd340b': 'Procedimentos',
    'c40a97fd70a543f1af4d960efabd340bDesc': 'Consulte os procedimentos da empresa',
    '2285fcbd70244f9a91c3e0a87de27ba0': 'Políticas',
    '2285fcbd70244f9a91c3e0a87de27ba0Desc': 'Consulte as políticas da empresa',
    '90e09b57c23e41499770e35c2d66cf7a': 'Calendário',
    '90e09b57c23e41499770e35c2d66cf7aDesc': 'Consulte o calendário de eventos',
    '01aa36f2d02e49ab903cf9a638b2f0ba': 'Notícias',
    '01aa36f2d02e49ab903cf9a638b2f0baDesc': 'Fique por dentro das novidades',
    '3e7b2395d70847de83aba8a7f61d0977': 'Reembolso',
    '3e7b2395d70847de83aba8a7f61d0977Desc': 'Solicite reembolso de despesas',
    '64ee7490519f42d2afbaf3063bbe1bdc': 'Contracheque',
    '64ee7490519f42d2afbaf3063bbe1bdcDesc': 'Acesse seus contracheques',
    '515e6360431d43b69877a1d0ca23296c': 'Ponto',
    '515e6360431d43b69877a1d0ca23296cDesc': 'Registre seu ponto',
    '5b07e529830c43be8f75dff38053744c': 'Avaliação',
    '5b07e529830c43be8f75dff38053744cDesc': 'Acesse suas avaliações',
    'e460055d4b674350a0155317fc07e76a': 'Admin',
    'e460055d4b674350a0155317fc07e76aDesc': 'Painel administrativo',

    // Versão com hífens (formato do banco de dados)
    '6377431f-4afa-448b-b46a-8321a5870f37': 'Manual',
    '6377431f-4afa-448b-b46a-8321a5870f37Desc': 'Acesse o manual da empresa',
    'c40a97fd-70a5-43f1-af4d-960efabd340b': 'Procedimentos',
    'c40a97fd-70a5-43f1-af4d-960efabd340bDesc': 'Consulte os procedimentos da empresa',
    '2285fcbd-7024-4f9a-91c3-e0a87de27ba0': 'Políticas',
    '2285fcbd-7024-4f9a-91c3-e0a87de27ba0Desc': 'Consulte as políticas da empresa',
    '90e09b57-c23e-4149-9770-e35c2d66cf7a': 'Calendário',
    '90e09b57-c23e-4149-9770-e35c2d66cf7aDesc': 'Consulte o calendário de eventos',
    '01aa36f2-d02e-49ab-903c-f9a638b2f0ba': 'Notícias',
    '01aa36f2-d02e-49ab-903c-f9a638b2f0baDesc': 'Fique por dentro das novidades',
    '3e7b2395-d708-47de-83ab-a8a7f61d0977': 'Reembolso',
    '3e7b2395-d708-47de-83ab-a8a7f61d0977Desc': 'Solicite reembolso de despesas',
    '64ee7490-519f-42d2-afba-f3063bbe1bdc': 'Contracheque',
    '64ee7490-519f-42d2-afba-f3063bbe1bdcDesc': 'Acesse seus contracheques',
    '515e6360-431d-43b6-9877-a1d0ca23296c': 'Ponto',
    '515e6360-431d-43b6-9877-a1d0ca23296cDesc': 'Registre seu ponto',
    '5b07e529-830c-43be-8f75-dff38053744c': 'Avaliação',
    '5b07e529-830c-43be-8f75-dff38053744cDesc': 'Acesse suas avaliações',
    'e460055d-4b67-4350-a015-5317fc07e76a': 'Admin',
    'e460055d-4b67-4350-a015-5317fc07e76aDesc': 'Painel administrativo',
  },
  ponto: {
    title: 'Ponto',
    pageTitle: 'Registro de Ponto',
    description: 'Registre seu ponto e consulte seu histórico',
    welcomeTitle: 'Bem-vindo ao Batida Online',
    welcomeDescription: 'O Batida Online é a plataforma utilizada pela ABZ Group para o registro de ponto dos colaboradores, automatizando processos e facilitando o dia a dia.',
    accessBatidaOnline: 'Acessar Batida Online (Web)',
    appAccess: 'Acesso via Aplicativos',
    ahgoraMultiDescription: 'Utilize este aplicativo para registrar seu ponto via reconhecimento facial (conforme configurações da empresa).',
    activationKey: 'Chave de Ativação:',
    activationKeyInfo: '(Informada no E-mail enviado pelo RH)',
    appStoreUnavailable: 'App Store link indisponível',
    myAhgoraDescription: 'Use este aplicativo para consultar seu espelho de ponto, solicitar ajustes e abonos.',
    companyCode: 'Código da Empresa:',
    registrationPassword: 'Matrícula/Senha:',
    webAccess: 'Acesso via Web',
    webAccessDescription: 'Você também pode acessar o sistema de ponto através do navegador.',
    additionalResources: 'Recursos Adicionais',
    manualDescription: 'Consulte o manual de uso para obter instruções detalhadas sobre como utilizar as plataformas Ahgora.',
    downloadManual: 'Baixar Manual (PDF)',
  },

  procedimentos: {
    title: 'Procedimentos Gerais',
    pageTitle: 'Procedimentos Gerais',
    comingSoon: 'Em Breve',
    description: 'Esta seção abrigará procedimentos e diretrizes gerais de diversos departamentos.',
    contentAvailable: 'O conteúdo será adicionado assim que estiver disponível.',
  },

  contracheque: {
    title: 'Consulta de Contracheque',
    pageTitle: 'Consulta de Contracheque',
    description: 'Acesse seus contracheques e informações salariais',
    accessSystem: 'Acessar Sistema Externo',
    systemDescription: 'Clique no botão abaixo para acessar o sistema de consulta de contracheques.',
    externalAccess: 'Você será redirecionado para um sistema externo para visualizar seus contracheques.',
  },

  calendario: {
    title: 'Calendário de Feriados',
    description: 'Feriados Nacionais e Municipais (Macaé, RJ)',
    loading: 'Carregando feriados...',
    loadingHolidays: 'Carregando...',
    couldNotLoadHolidays: 'Não foi possível carregar feriados.',
    noHolidaysThisMonth: 'Nenhum feriado neste mês.',
    nationalHoliday: 'Feriado Nacional / Outro',
    municipalHoliday: 'Feriado Municipal (Macaé)',
    holidaysInMonth: 'Feriados em',
    failedToFetchBrasilApi: 'Falha ao buscar na BrasilAPI',
    tryingAlternative: 'Tentando alternativa...',
    failedToFetchFromAllSources: 'Falha ao buscar feriados de todas as fontes.',
  },

  manual: {
    title: 'Manual do Colaborador (Logística)',
    description: 'Guia completo com as diretrizes e informações sobre os processos logísticos.',
    download: 'Download (PDF)',
    view: 'Visualizar',
    noManuals: 'Nenhum manual encontrado',
    mainDocument: 'Documento Principal',
  },
  procedures: {
    title: 'Procedimentos de Logística (Revisado)',
    description: 'Documento detalhado com os procedimentos operacionais padrão revisados para a área.',
    download: 'Download (PDF)',
    view: 'Visualizar',
    noProcedures: 'Nenhum procedimento encontrado',
    mainDocument: 'Documento Principal',
  },
  policies: {
    title: 'Políticas',
    description: 'Políticas da empresa',
    download: 'Download',
    view: 'Visualizar',
    noPolicies: 'Nenhuma política encontrada',
    documentsTitle: 'Documentos de Políticas',
    hse: {
      title: 'Política de HSE',
      description: 'Diretrizes de Saúde, Segurança e Meio Ambiente do ABZ Group'
    },
    quality: {
      title: 'Política da Qualidade',
      description: 'Política de Qualidade e Gestão do ABZ Group',
      titleEn: 'Quality Policy',
      descriptionEn: 'ABZ Group Quality Management Policy',
      category: 'Qualidade'
    },
  },

  news: {
    title: 'ABZ News e Comunicados',
    description: 'Últimas notícias da empresa',
    readMore: 'Ler mais',
    noNews: 'Nenhuma notícia ou comunicado disponível no momento.',
    publishedAt: 'Publicado em',
    by: 'por',
    view: 'Visualizar',
    download: 'Download (PDF)',
    examples: {
      title1: 'Exemplo de Notícia 1',
      description1: 'Breve descrição do conteúdo desta notícia ou cartilha.',
      title2: 'Exemplo de Notícia 2',
      description2: 'Outro exemplo de um comunicado importante em formato PDF.'
    }
  },
  locale: {
    code: 'pt-BR'
  },
  viewer: {
    loading: 'Carregando documento...',
    error: 'Erro ao carregar o documento',
    download: 'Baixar documento',
    downloadView: 'Baixar e visualizar localmente',
    fullscreen: 'Tela cheia',
    exitFullscreen: 'Sair da tela cheia',
    close: 'Fechar visualizador',
    browserNotSupported: 'Seu navegador não suporta visualização de PDF',
    downloadPrompt: 'Por favor, baixe o documento para visualizá-lo',
    downloadPdf: 'Baixar PDF',
    width: 'Largura',
    height: 'Altura',
    page: 'Página',
    fitWidth: 'Ajustar à largura',
    fitHeight: 'Ajustar à altura',
    fitPage: 'Ajustar à página',
    showPdf: 'Visualizar PDF',
    showContent: 'Visualizar conteúdo extraído',
    extracting: 'Extraindo conteúdo do documento...',
    extractError: 'Erro ao extrair conteúdo',
    noContent: 'Nenhum conteúdo extraído',
    zoomIn: 'Aumentar zoom',
    zoomOut: 'Diminuir zoom',
    rotate: 'Girar',
    fileNotFound: 'Arquivo não encontrado'
  },
  reimbursement: {
    title: 'Reembolso',
    description: 'Solicite reembolso de despesas',
    tabs: {
      request: 'Solicitar Reembolso',
      dashboard: 'Meus Reembolsos',
      approval: 'Aprovar Reembolsos'
    },
    loadingDashboard: 'Carregando painel de reembolsos...',
    loadingApproval: 'Carregando aprovações de reembolsos...',
    goToDashboard: 'Ir para o painel de reembolsos',
    goToApproval: 'Ir para aprovações de reembolsos',
    redirectingToDashboard: 'Redirecionando para o painel de reembolsos...',
    redirectingToApproval: 'Redirecionando para aprovações de reembolsos...',
    approvalPermissionRequired: 'Você não tem permissão para acessar a página de aprovação de reembolsos.',
    form: {
      title: 'Formulário de Reembolso',
      personalInfo: 'Dados Pessoais',
      fullName: 'Nome Completo',
      email: 'Email',
      emailLocked: 'Email bloqueado (usuário logado)',
      phone: 'Telefone',
      cpf: 'CPF',
      position: 'Cargo',
      costCenter: 'Centro de Custo',
      expenseInfo: 'Dados da Despesa',
      expenseType: 'Tipo de Despesa',
      expenseDate: 'Data da Despesa',
      expenseValue: 'Valor da Despesa',
      amount: 'Valor',
      description: 'Descrição',
      descriptionPlaceholder: 'Descreva a despesa...',
      expenseDescription: 'Descrição da Despesa',
      receipts: 'Comprovantes',
      attachments: 'Anexos',
      addAttachment: 'Adicionar anexo',
      removeAttachment: 'Remover anexo',
      bankInfo: 'Dados Bancários',
      bankName: 'Nome do Banco',
      accountType: 'Tipo de Conta',
      checking: 'Corrente',
      savings: 'Poupança',
      agency: 'Agência',
      account: 'Conta',
      pixKey: 'Chave PIX',
      pixKeyType: 'Tipo de Chave PIX',
      cpfKey: 'CPF',
      emailKey: 'Email',
      phoneKey: 'Telefone',
      randomKey: 'Chave Aleatória',
      submit: 'Enviar Solicitação',
      submitting: 'Enviando...',
      submitSuccess: 'Solicitação enviada com sucesso',
      submitError: 'Erro ao enviar solicitação',
      protocol: 'Protocolo',
      thankYou: 'Obrigado pela sua solicitação',
      thankYouMessage: 'Sua solicitação foi recebida e será processada em breve.',
      viewPolicy: 'Ver Política de Reembolso',
      notes: 'Observações (opcional)',
      pixKeyPlaceholder: 'Digite sua chave PIX',
      pixCpfPlaceholder: '000.000.000-00',
      pixEmailPlaceholder: '<EMAIL>',
      pixPhonePlaceholder: '(00) 00000-0000',
      pixRandomPlaceholder: 'Chave aleatória PIX',
      costCenterRequired: 'Centro de custo é obrigatório',
      paymentInfo: 'Informações de Pagamento',
      expenses: 'Despesas',
      expense: 'Despesa',
      authStatus: {
        loginRequired: 'Login necessário',
        loginRequiredMessage: 'Você precisa estar logado para enviar um reembolso.',
        loginLink: 'Clique aqui para fazer login',
        authenticated: 'Você está logado e pode enviar reembolsos',
        redirectingToLogin: 'Você precisa estar logado para enviar um reembolso. Redirecionando para login...',
      },
    },

    policy: {
      title: 'Política de Reembolso - ABZ Group',
      introduction: 'Este documento estabelece as diretrizes e procedimentos para solicitação e processamento de reembolsos de despesas relacionadas às atividades profissionais dos colaboradores da ABZ Group.',
      eligibility: '1. Elegibilidade',
      eligibilityText: 'São elegíveis para reembolso as despesas diretamente relacionadas às atividades profissionais, previamente autorizadas pelo gestor responsável, e que estejam em conformidade com as políticas da empresa.',
      typesTitle: '2. Tipos de Despesas Reembolsáveis',
      foodLabel: 'Alimentação:',
      foodText: 'Refeições durante viagens a trabalho ou reuniões externas.',
      transportLabel: 'Transporte:',
      transportText: 'Deslocamentos a serviço, incluindo táxi, aplicativos de transporte, combustível, pedágios e estacionamento.',
      accommodationLabel: 'Hospedagem:',
      accommodationText: 'Estadias em hotéis durante viagens a trabalho.',
      materialsLabel: 'Material de Trabalho:',
      materialsText: 'Itens necessários para execução das atividades profissionais.',
      otherLabel: 'Outras despesas:',
      otherText: 'Conforme aprovação prévia do gestor.',
      documentationTitle: '3. Documentação Necessária',
      documentationText: 'Para todas as solicitações de reembolso, é obrigatória a apresentação de comprovantes fiscais válidos (notas fiscais, cupons fiscais, recibos) que contenham:',
      docDate: 'Data e hora da despesa',
      docAmount: 'Valor',
      docDescription: 'Descrição do produto ou serviço',
      docSupplier: 'Identificação do fornecedor (CNPJ ou CPF)',
      deadlinesTitle: '4. Prazos',
      deadlinesText: 'As solicitações de reembolso devem ser apresentadas em até 30 dias após a realização da despesa. O processamento será realizado em até 10 dias úteis após a aprovação do gestor.',
      paymentTitle: '5. Formas de Pagamento',
      paymentText: 'O reembolso será realizado preferencialmente por:',
      paymentBank: 'Depósito em conta bancária do colaborador',
      paymentPix: 'Transferência via PIX',
      paymentCash: 'Em casos excepcionais, pagamento em espécie pelo agente financeiro',
      restrictionsTitle: '6. Restrições',
      restrictionsText: 'Não serão reembolsadas despesas:',
      restrictionProof: 'Sem comprovação fiscal adequada',
      restrictionPersonal: 'De caráter pessoal',
      restrictionAuth: 'Não autorizadas previamente (quando aplicável)',
      restrictionPolicy: 'Em desacordo com as políticas da empresa',
      restrictionDoc: 'Com documentação ilegível ou incompleta',
      finalTitle: '7. Considerações Finais',
      finalText: 'A ABZ Group se reserva o direito de auditar as solicitações de reembolso a qualquer momento. Casos omissos serão analisados pela Diretoria Financeira. Esta política pode ser revisada e atualizada periodicamente.',
      agreeButton: 'Entendi e Concordo'
    },
    rejectionReasonRequired: 'O motivo da rejeição é obrigatório',
    confirmRejection: 'Confirmar Rejeição',
    rejectionReasonPlaceholder: 'Informe o motivo da rejeição...',
    modal: {
      title: 'Detalhes do Reembolso',
      applicantInfo: 'Informações do Solicitante',
      reimbursementInfo: 'Informações do Reembolso',
      description: 'Descrição',
      paymentMethod: 'Método de Pagamento',
      receipts: 'Comprovantes',
      history: 'Histórico',
      rejectionReason: 'Motivo da Rejeição',
      rejectionReasonDescription: 'Por favor, informe o motivo da rejeição. Esta informação será enviada ao solicitante.',
      name: 'Nome',
      email: 'Email',
      phone: 'Telefone',
      cpf: 'CPF',
      position: 'Cargo',
      costCenter: 'Centro de Custo',
      protocol: 'Protocolo',
      type: 'Tipo',
      value: 'Valor',
      date: 'Data',
      status: 'Status',
      method: 'Método',
      bank: 'Banco',
      agency: 'Agência',
      account: 'Conta',
      pixType: 'Tipo PIX',
      pixKey: 'Chave PIX',
      notInformed: 'Não informado',
      downloadFile: 'Baixar arquivo',
      approve: 'Aprovar',
      reject: 'Rejeitar',
      cancel: 'Cancelar',
      close: 'Fechar'
    }
  },
  admin: {
    title: 'Administração',
    dashboard: 'Painel de Administração',
    systemSetup: 'Setup do Sistema',
    welcomeAdmin: 'Bem-vindo, {name}. Gerencie o conteúdo e as configurações do sistema.',
    cards: 'Cards',
    cardsDesc: 'Gerencie os cards exibidos no dashboard principal.',
    menu: 'Menu',
    menuDesc: 'Configure os itens do menu lateral.',
    documentsSection: 'Documentos',
    documentsDesc: 'Gerencie documentos, políticas e manuais.',
    news: 'Notícias',
    newsDesc: 'Adicione e edite notícias e comunicados.',
    addNews: 'Adicionar Notícia',
    editNews: 'Editar Notícia',
    errorLoadingNews: 'Erro ao carregar notícia. Por favor, tente novamente.',
    errorSavingNews: 'Erro ao salvar notícia. Por favor, tente novamente.',
    errorDeletingNews: 'Erro ao excluir notícia. Por favor, tente novamente.',
    noNewsFound: 'Nenhuma notícia encontrada. Clique em "Adicionar Notícia" para criar uma nova.',
    addMenuItem: 'Adicionar Item de Menu',
    noItems: 'Nenhum item encontrado. Clique em "Adicionar Item" para criar um novo.',
    addNewEmail: 'Adicionar novo email',
    refresh: 'Atualizar',
    uploading: 'Enviando...',
    upload: 'Enviar',
    addAuthorization: 'Adicionar Autorização',
    generateCode: 'Gerar Código',
    refreshUserList: 'Atualizar lista de usuários',
    cardsTable: 'Tabela de Cards',
    cardsTableDescription: 'Este utilitário permite criar a tabela de cards no banco de dados Supabase. A tabela é necessária para armazenar os cards do dashboard.',
    creatingCardsTable: 'Criando tabela de cards...',
    errorCreatingCardsTable: 'Erro ao criar tabela de cards',
    cardsTableCreatedSuccess: 'Tabela de cards criada com sucesso',
    checkingCardsTable: 'Verificando tabela de cards...',
    errorCheckingCardsTable: 'Erro ao verificar tabela de cards',
    cardsTableExists: 'A tabela de cards já existe no banco de dados',
    cardsTableNotExists: 'A tabela de cards não existe. Clique no botão para criá-la.',
    createCardsTable: 'Criar Tabela de Cards',
    checkTable: 'Verificar Tabela',
    cardsMigration: 'Migração de Cards',
    cardsMigrationDescription: 'Este utilitário permite migrar os cards hardcoded do código-fonte para o banco de dados Supabase. Isso permite que os cards sejam editados através da interface administrativa.',
    checkingMigrationStatus: 'Verificando status da migração...',
    errorCheckingMigrationStatus: 'Erro ao verificar status da migração',
    migrationStatusCheckedSuccess: 'Status da migração verificado com sucesso',
    migratingCards: 'Migrando cards para o banco de dados...',
    errorMigratingCards: 'Erro ao migrar cards',
    cardsMigratedSuccess: 'Cards migrados com sucesso',
    migrateCards: 'Migrar Cards',
  },
  fileUploader: {
    attachments: 'Comprovantes',
    dragDropText: 'Arraste e solte arquivos aqui, ou clique para selecionar',
    acceptedFormats: 'Formatos aceitos: PDF, JPG, PNG (máx. {maxSize}MB por arquivo)',
    selectedFiles: 'Arquivos selecionados ({count}/{max})',
    uploading: 'Enviando...',
    unsupportedFileType: 'Tipo de arquivo não suportado: {type}',
    fileTooLarge: 'Arquivo muito grande: {size}MB (máximo: {maxSize}MB)',
    maxFilesExceeded: 'Você pode enviar no máximo {maxFiles} arquivos',
    errorReadingFile: 'Erro ao ler o arquivo',
    fileStoredLocally: 'Arquivo armazenado localmente devido a problemas de armazenamento. O arquivo será incluído na sua solicitação, mas pode não ser armazenado permanentemente.',
    uploadError: 'Erro ao fazer upload',
    errorUploadingFile: 'Erro ao fazer upload de {fileName}: {error}',
  },
  starRating: {
    ariaLabel: '{star} de {maxRating} estrelas',
  },
  evaluation: {
    noPermission: 'Você não tem permissão para acessar o módulo de avaliação.',
    errorLoadingEvaluations: 'Erro ao carregar avaliações',
    viewNotConfigured: 'A visualização de avaliações não está configurada. Entre em contato com o administrador.',
    noPermissionAccess: 'Você não tem permissão para acessar as avaliações.',
    evaluator: 'Avaliador',
    period: 'Período',
    dashboard: {
      title: 'Avaliações de Desempenho',
    },
    status: {
      pending: 'Pendente',
      inProgress: 'Em Andamento',
      completed: 'Concluída',
      cancelled: 'Cancelada',
    },
  },
  userEditor: {
    newUser: 'Novo Usuário',
    editUser: 'Editar Usuário',
    requiredFields: 'Número de telefone, nome e sobrenome são obrigatórios',
    passwordRequired: 'A senha é obrigatória para novos usuários',
    passwordMismatch: 'As senhas não coincidem',
    usersSection: 'Gerenciamento de Usuários',
    usersSectionDesc: 'Gerencie usuários, permissões e autorizações de acesso.',
    authorizedUsers: 'Usuários Autorizados',
    authorizedUsersDesc: 'Gerencie solicitações de acesso e usuários autorizados.',
    settings: 'Configurações',
    settingsDesc: 'Configure as opções do sistema.',
    cardManagement: 'Gerenciamento de Cards',
    cardManagementDesc: 'Adicione, edite ou remova os cards exibidos no dashboard.',
    addCard: 'Adicionar Card',
    menuManagement: 'Gerenciamento de Menu',
    menuManagementDesc: 'Adicione, edite ou remova os itens do menu lateral.',
    addMenuItem: 'Adicionar Item',
    noItems: 'Nenhum item encontrado. Clique em "Adicionar Item" para criar um novo.',
    systemInfo: 'Informações do Sistema',
    version: 'Versão',
    environment: 'Ambiente',
    production: 'Produção',
    lastUpdate: 'Última atualização',
    database: 'Banco de dados',
    status: 'Status',
    online: 'Online',
    offline: 'Offline',
    activeUsers: 'Usuários ativos',
    lastLogin: 'Último Login',
    active: 'Ativo',
    inactive: 'Inativo',
    manage: 'Gerenciar',
    users: {
      title: 'Usuários',
      addUser: 'Adicionar Usuário',
      editUser: 'Editar Usuário',
      deleteUser: 'Excluir Usuário',
      deleteConfirm: 'Tem certeza que deseja excluir este usuário?',
      userDeleted: 'Usuário excluído com sucesso',
      userAdded: 'Usuário adicionado com sucesso',
      userUpdated: 'Usuário atualizado com sucesso',
      userDetails: 'Detalhes do Usuário',
      personalInfo: 'Informações Pessoais',
      firstName: 'Nome',
      lastName: 'Sobrenome',
      email: 'Email',
      phone: 'Telefone',
      role: 'Função',
      admin: 'Administrador',
      manager: 'Gerente',
      user: 'Usuário',
      department: 'Departamento',
      position: 'Cargo',
      status: 'Status',
      active: 'Ativo',
      inactive: 'Inativo',
      createdAt: 'Criado em',
      updatedAt: 'Atualizado em',
      lastLogin: 'Último login',
      resetPassword: 'Redefinir Senha',
      passwordReset: 'Senha redefinida com sucesso',
      accessHistory: 'Histórico de Acesso',
      noHistory: 'Nenhum histórico encontrado',
      permissions: 'Permissões',
      modules: 'Módulos',
      features: 'Funcionalidades',
    },
    documents: {
      section: 'Documentos',
      description: 'Adicione, edite ou remova documentos, políticas e manuais.',
      addDocument: 'Adicionar Documento',
      editDocument: 'Editar Documento',
      deleteDocument: 'Excluir Documento',
      deleteConfirm: 'Tem certeza que deseja excluir este documento?',
      documentDeleted: 'Documento excluído com sucesso',
      documentAdded: 'Documento adicionado com sucesso',
      documentUpdated: 'Documento atualizado com sucesso',
      documentDetails: 'Detalhes do Documento',
      docTitle: 'Título',
      category: 'Categoria',
      language: 'Idioma',

      file: 'Arquivo',
      filePlaceholder: 'Caminho do arquivo ou URL',
      select: 'Selecionar',
      active: 'Ativo',
      add: 'Adicionar',
      edit: 'Editar',
      delete: 'Excluir',
      moveUp: 'Mover para cima',
      moveDown: 'Mover para baixo',
      enable: 'Ativar',
      disable: 'Desativar',
      download: 'Baixar',
      filterByCategory: 'Filtrar por Categoria',
      all: 'Todas',
      loading: 'Carregando documentos...',
      noItems: 'Nenhum documento encontrado. Clique em "Adicionar Documento" para criar um novo.',
      selectLanguage: 'Selecione um idioma',
      portuguese: 'Português',
      english: 'English',
      spanish: 'Español',
      order: 'Ordem',
      errorLoading: 'Erro ao carregar documentos',
      errorLoadingMessage: 'Erro ao carregar documentos. Por favor, tente novamente.',
      uploadFile: 'Enviar arquivo',
      replaceFile: 'Substituir arquivo',
      enabled: 'Habilitado',
      disabled: 'Desabilitado',

      createdAt: 'Criado em',
      updatedAt: 'Atualizado em',
    },
  },
  contact: {
    title: 'Contato',
    description: 'Entre em contato conosco',
    name: 'Nome',
    email: 'Email',
    phone: 'Telefone',
    message: 'Mensagem',
    send: 'Enviar',
    sending: 'Enviando...',
    success: 'Mensagem enviada com sucesso',
    error: 'Erro ao enviar mensagem',
    needHelp: 'Precisa de ajuda?',
    helpMessage: 'Se você tiver dúvidas sobre o formulário de reembolso ou precisar de assistência, entre em contato conosco:',
    businessHours: 'Horário de Atendimento',
    businessHoursTime: 'Segunda a Sexta, das 9h às 18h',
  },
  errors: {
    notFound: 'Página não encontrada',
    serverError: 'Erro interno do servidor',
    unauthorized: 'Não autorizado',
    forbidden: 'Acesso negado',
    badRequest: 'Requisição inválida',
    conflict: 'Conflito',
    validation: 'Erro de validação',
    timeout: 'Tempo limite excedido',
    offline: 'Você está offline',
    unknown: 'Erro desconhecido',
    pageNotFound: 'Página não encontrada',
    pageNotFoundMessage: 'A página que você está procurando não existe ou foi movida.',
    errorOccurred: 'Ocorreu um erro',
    somethingWentWrong: 'Algo deu errado. Por favor, tente novamente.',
    errorCode: 'Código de erro',
  },
  common: {
    loading: 'Carregando...',
    error: 'Ocorreu um erro',
    success: 'Operação realizada com sucesso',
    save: 'Salvar',
    cancel: 'Cancelar',
    delete: 'Excluir',
    edit: 'Editar',
    view: 'Visualizar',
    search: 'Buscar',
    filter: 'Filtrar',
    close: 'Fechar',
    confirm: 'Confirmar',
    back: 'Voltar',
    next: 'Próximo',
    previous: 'Anterior',
    submit: 'Enviar',
    help: 'Ajuda',
    copy: 'Copiar protocolo',
    copied: 'Copiado!',
    done: 'Concluir',
    required: 'Campo obrigatório',
    invalidEmail: 'Email inválido',
    invalidPhone: 'Telefone inválido',
    email: 'Email',
    phone: 'Telefone',
    invalidCPF: 'CPF inválido',
    invalidDate: 'Data inválida',
    invalidValue: 'Valor inválido',
    yes: 'Sim',
    no: 'Não',
    all: 'Todos',
    none: 'Nenhum',
    select: 'Selecione',
    selectOption: 'Selecione uma opção',
    noResults: 'Nenhum resultado encontrado',
    manage: 'Gerenciar',
    noData: 'Nenhum dado encontrado',
    welcome: 'Bem-vindo',
    actions: 'Ações',
    approve: 'Aprovar',
    reject: 'Rejeitar',
    showing: 'Mostrando',
    to: 'a',
    of: 'de',
    results: 'resultados',
    refresh: 'Atualizar',
    redirecting: 'Redirecionando...',
    accessDenied: 'Acesso Negado',
    tryAgain: 'Tentar novamente',
    logout: 'Sair',
    login: 'Entrar',
    password: 'Senha',
    confirmPassword: 'Confirmar senha',
    forgotPassword: 'Esqueceu a senha?',
    resetPassword: 'Redefinir senha',
    changePassword: 'Alterar senha',
    newPassword: 'Nova senha',
    currentPassword: 'Senha atual',
    passwordMismatch: 'As senhas não coincidem',
    passwordChanged: 'Senha alterada com sucesso',
    passwordExpired: 'Sua senha expirou e precisa ser alterada',
    profile: 'Perfil',
    settings: 'Configurações',
    language: 'Idioma',
    theme: 'Tema',
    dark: 'Escuro',
    light: 'Claro',
    system: 'Sistema',
    chooseLanguage: 'Escolha seu idioma',
    portuguese: 'Português',
    english: 'Inglês',

    rights: 'os direitos',
    reserved: 'reservados',
    developedBy: 'Desenvolvido por',
    backToDashboard: 'Voltar para o Dashboard',
    processing: 'Processando...',
    saveSettings: 'Salvar Configurações',
    resetPasswordFor: 'Redefinir Senha -',
    errorResettingPassword: 'Erro ao redefinir senha',
    errorSavingSettings: 'Erro ao salvar configurações',
    settingsSavedSuccess: 'Configurações salvas com sucesso',
    unknownError: 'Erro desconhecido',
    portugueseBrazil: 'Português (Brasil)',
    englishUS: 'English (US)',
    spanish: 'Español',
    notifications: 'Notificações',
    enabled: 'Ativado',
    professionalInfo: 'Informações Profissionais',
    position: 'Cargo',
    department: 'Departamento',
    systemRole: 'Função no sistema',
    notInformed: 'Não informado',
    preferences: 'Preferências',
    administrator: 'Administrador',
    manager: 'Gerente',
    user: 'Usuário',
    // Missing keys from English
    allItems: 'Todos',
    text: 'Texto',
    register: 'Registrar',
    comingSoon: 'Em breve! Esta funcionalidade está em desenvolvimento.',
    backToLogin: 'Voltar ao Login',
    allRights: 'Todos',
    administrators: 'Administradores',
    managers: 'Gerentes',
    users: 'Usuários',
    voltar: 'Voltar',
    saving: 'Salvando...',

  },
  payroll: {
    title: 'Folha de Pagamento',
    description: 'Gestão completa de folha de pagamento',
    newPayroll: 'Nova Folha',
    quickActions: 'Ações Rápidas',
    newPayrollSheet: 'Nova Folha de Pagamento',
    manageEmployees: 'Gerenciar Funcionários',
    manageCompanies: 'Gerenciar Empresas',
    reports: 'Relatórios',
    monthlyReport: 'Relatório Mensal',
    paymentGuides: 'Guias de Recolhimento',
    costAnalysis: 'Análise de Custos',
    settings: 'Configurações',
    payrollCodes: 'Códigos de Folha',
    calculationProfiles: 'Perfis de Cálculo',
    legalTables: 'Tabelas Legais',
    recentSheets: 'Folhas Recentes',
    noPayrollSheets: 'Nenhuma folha de pagamento encontrada',
    createFirstSheet: 'Criar primeira folha',
    totalMonthly: 'Total Mensal',
    employees: 'Funcionários',
    companies: 'Empresas',
    activeSheets: 'Folhas Ativas',
  },
  debug: {
    noCodesFound: 'Nenhum código ativo encontrado.',
  },
  manager: {
    moduleTitle: 'Módulo Gerencial',
    moduleDescription: 'Ferramentas e recursos especiais para gerentes',
    welcome: 'Bem-vindo ao Módulo Gerencial',
    moduleIntro: 'Este módulo fornece ferramentas e recursos especiais projetados especificamente para gerentes. Aqui você pode acessar relatórios avançados, ferramentas de gestão de equipe e outros recursos para ajudá-lo a gerenciar sua equipe de forma eficaz.',
  },
};
